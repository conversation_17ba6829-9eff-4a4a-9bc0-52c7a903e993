import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import LoginPage from './pages/LoginPage';
import Dashboard from './pages/Dashboard';
import Home from './pages/Home';
import ReportDetails from './pages/ReportDetails';
import ProtectedRoute from './components/ProtectedRoute';
import ErrorBoundary from './components/ErrorBoundary';

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
});

function App() {
  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <Router
          future={{
            v7_startTransition: true,
            v7_relativeSplatPath: true,
          }}
        >
          <div className="min-h-screen bg-gray-50">
            <Routes>
              <Route path="/" element={<Home />} />
              <Route path="/login" element={<LoginPage />} />
              <Route
                path="/dashboard"
                element={
                  <ProtectedRoute>
                    <Dashboard />
                  </ProtectedRoute>
                }
              />
              {/* Report Details Route */}
              <Route path="/reports/:id" element={<ReportDetails />} />
              {/* Placeholder routes for navigation links */}
              <Route path="/reports" element={<div className="p-8 text-center">Reports page coming soon...</div>} />
              <Route path="/report/new" element={<div className="p-8 text-center">Report submission page coming soon...</div>} />
              <Route path="/volunteer" element={<div className="p-8 text-center">Volunteer page coming soon...</div>} />
              <Route path="*" element={<Navigate to="/" replace />} />
            </Routes>
          </div>
        </Router>
       </QueryClientProvider>
    </ErrorBoundary>
  );
}

export default App
