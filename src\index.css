@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  }
  
  body {
    margin: 0;
    min-height: 100vh;
    background-color: #f9fafb;
  }
}

@layer components {
  .btn-primary {
    @apply bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors;
  }
  
  .btn-secondary {
    @apply bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors;
  }
  
  .input-field {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors;
  }
  
  .card {
    @apply bg-white overflow-hidden shadow rounded-lg;
  }

  /* Leaflet map styles to ensure proper zoom functionality */
  .leaflet-container {
    touch-action: pan-x pan-y !important;
  }

  .leaflet-container .leaflet-control-zoom {
    border: none !important;
    border-radius: 8px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  }

  .leaflet-container .leaflet-control-zoom a {
    border-radius: 6px !important;
    border: none !important;
    background-color: white !important;
    color: #374151 !important;
    font-weight: 600 !important;
    transition: all 0.2s ease !important;
  }

  .leaflet-container .leaflet-control-zoom a:hover {
    background-color: #f3f4f6 !important;
    color: #1f2937 !important;
  }
}
