@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  }
  
  body {
    margin: 0;
    min-height: 100vh;
    background-color: #f9fafb;
  }
}

@layer components {
  .btn-primary {
    @apply bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors;
  }
  
  .btn-secondary {
    @apply bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors;
  }
  
  .input-field {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors;
  }
  
  .card {
    @apply bg-white overflow-hidden shadow rounded-lg;
  }

  /* Leaflet map styles to ensure proper zoom functionality and z-index layering */
  .leaflet-container {
    touch-action: pan-x pan-y !important;
    /* Ensure map container stays below header (z-50 = 50) */
    z-index: 10 !important;
  }

  /* Control all Leaflet elements to stay below header */
  .leaflet-control-container {
    z-index: 15 !important;
  }

  .leaflet-control {
    z-index: 15 !important;
  }

  .leaflet-popup-pane {
    z-index: 20 !important;
  }

  .leaflet-tooltip-pane {
    z-index: 25 !important;
  }

  .leaflet-marker-pane {
    z-index: 12 !important;
  }

  .leaflet-tile-pane {
    z-index: 5 !important;
  }

  .leaflet-container .leaflet-control-zoom {
    border: none !important;
    border-radius: 8px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    z-index: 15 !important;
  }

  .leaflet-container .leaflet-control-zoom a {
    border-radius: 6px !important;
    border: none !important;
    background-color: white !important;
    color: #374151 !important;
    font-weight: 600 !important;
    transition: all 0.2s ease !important;
  }

  .leaflet-container .leaflet-control-zoom a:hover {
    background-color: #f3f4f6 !important;
    color: #1f2937 !important;
  }

  /* Ensure map sections don't interfere with header */
  .map-section {
    position: relative;
    z-index: 1;
  }

  /* Ensure header always stays on top */
  header {
    position: sticky !important;
    z-index: 50 !important;
  }
}
