import React, { useState } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { 
  ArrowLeft, 
  MapPin, 
  Calendar, 
  User, 
  Heart, 
  MessageCircle, 
  CheckCircle, 
  Clock,
  Camera,
  AlertTriangle,
  Shield,
  Navigation,
  ZoomIn,
  ZoomOut,
  ExternalLink,
  Info,
  Package,
  Users,
  ChevronLeft
} from 'lucide-react';
import { mockReports } from '../data/mockData';
import { useAuth } from '../context/AuthContext';
import { format } from 'date-fns';
import ReportMap from '../components/Map/ReportMap';

const ReportDetail: React.FC = () => {
  const { reportId } = useParams<{ reportId: string }>();
  const { user } = useAuth();
  const [selectedPhotoIndex, setSelectedPhotoIndex] = useState(0);
  const [assistanceText, setAssistanceText] = useState('');
  const [showAssistanceForm, setShowAssistanceForm] = useState(false);

  const report = mockReports.find(r => r.id === reportId);

  if (!report) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-gray-50 to-slate-100 flex items-center justify-center">
        <div className="text-center bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-12">
          <AlertTriangle size={64} className="text-red-400 mx-auto mb-6" />
          <h1 className="text-3xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent mb-4">Report Not Found</h1>
          <p className="text-slate-600 mb-8">The report you're looking for doesn't exist or has been removed.</p>
          <Link 
            to="/reports" 
            className="inline-flex items-center gap-2 bg-gradient-to-r from-red-600 to-red-700 text-white px-6 py-3 rounded-xl font-semibold hover:from-red-700 hover:to-red-800 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-[1.02]"
          >
            <ArrowLeft size={20} />
            Back to Reports
          </Link>
        </div>
      </div>
    );
  }

  const handleOfferAssistance = () => {
    if (!assistanceText.trim()) return;
    
    // Here you would normally make an API call
    console.log('Offering assistance:', assistanceText);
    setAssistanceText('');
    setShowAssistanceForm(false);
    alert('Thank you for offering assistance! Your offer has been recorded.');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-gray-50 to-slate-100 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Enhanced Header */}
        <div className="flex items-center mb-10">
          <Link
            to="/reports"
            className="group flex items-center gap-2 text-slate-600 hover:text-slate-800 transition-all duration-200 hover:translate-x-[-2px]"
          >
            <div className="p-2 rounded-lg group-hover:bg-slate-100 transition-colors">
              <ArrowLeft size={20} />
            </div>
            <span className="font-semibold">Back to Reports</span>
          </Link>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Enhanced Basic Info */}
            <div className="group bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 hover:shadow-2xl transition-all duration-300 p-8">
              <div className="flex items-start justify-between mb-6">
                <div className="flex-1">
                  <h1 className="text-4xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent mb-4 leading-tight">
                    {report.title}
                  </h1>
                  <div className="flex flex-wrap items-center gap-4 text-slate-600 mb-6">
                    <div className="flex items-center gap-2 bg-white/60 backdrop-blur-sm px-3 py-2 rounded-lg border border-white/20">
                      <MapPin size={16} className="text-blue-500" />
                      <span className="font-medium">{report.location.address}</span>
                    </div>
                    <div className="flex items-center gap-2 bg-white/60 backdrop-blur-sm px-3 py-2 rounded-lg border border-white/20">
                      <Calendar size={16} className="text-purple-500" />
                      <span className="font-medium">{format(report.createdAt, 'MMMM d, yyyy')}</span>
                    </div>
                    <div className="flex items-center gap-2 bg-white/60 backdrop-blur-sm px-3 py-2 rounded-lg border border-white/20">
                      <User size={16} className="text-green-500" />
                      <span className="font-medium">{report.reporterName}</span>
                    </div>
                  </div>
                </div>
                <div className="flex flex-col gap-3">
                  <span className={`px-4 py-2 rounded-full text-sm font-bold text-white shadow-lg ${
                    report.status === 'verified' ? 'bg-gradient-to-r from-green-500 to-emerald-500' :
                    report.status === 'pending' ? 'bg-gradient-to-r from-yellow-500 to-amber-500' :
                    'bg-gradient-to-r from-red-500 to-red-600'
                  }`}>
                    {report.status === 'verified' && <CheckCircle size={16} className="inline mr-2" />}
                    {report.status === 'pending' && <Clock size={16} className="inline mr-2" />}
                    {report.status.charAt(0).toUpperCase() + report.status.slice(1)}
                  </span>
                </div>
              </div>

              <div className="flex flex-wrap gap-3 mb-6">
                <span className="bg-gradient-to-r from-red-100 to-red-50 text-red-800 px-4 py-2 rounded-full text-sm font-semibold border border-red-200 shadow-sm">
                  🚨 {report.disasterDetail}
                </span>
                <span className="bg-gradient-to-r from-blue-100 to-blue-50 text-blue-800 px-4 py-2 rounded-full text-sm font-semibold border border-blue-200 shadow-sm">
                  📍 {report.disasterType}
                </span>
              </div>

              <div className="bg-gradient-to-r from-slate-50 to-gray-50 rounded-xl p-6 border border-slate-100">
                <p className="text-slate-700 leading-relaxed text-lg font-medium">{report.description}</p>
              </div>
            </div>

            {/* Enhanced Photos Section */}
            <div className="group bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 hover:shadow-2xl transition-all duration-300">
              <div className="p-8 border-b border-slate-100">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg">
                    <Camera size={20} className="text-white" />
                  </div>
                  <h2 className="text-2xl font-bold text-slate-800">Impact Photos</h2>
                </div>
              </div>
              <div className="p-8">
                <div className="space-y-6">
                  <div className="relative aspect-video overflow-hidden rounded-xl shadow-lg">
                    <img
                      src={report.photos[selectedPhotoIndex]}
                      alt={`${report.title} - Photo ${selectedPhotoIndex + 1}`}
                      className="w-full h-full object-cover hover:scale-105 transition-transform duration-500"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>
                    <div className="absolute bottom-4 left-4 bg-white/80 backdrop-blur-sm px-3 py-2 rounded-lg border border-white/20">
                      <p className="text-sm font-semibold text-slate-800">Photo {selectedPhotoIndex + 1} of {report.photos.length}</p>
                    </div>
                  </div>
                  {report.photos.length > 1 && (
                    <div className="flex space-x-3 overflow-x-auto pb-2">
                      {report.photos.map((photo, index) => (
                        <button
                          key={index}
                          onClick={() => setSelectedPhotoIndex(index)}
                          className={`flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden border-2 transition-all duration-200 ${
                            selectedPhotoIndex === index 
                              ? 'border-blue-500 shadow-lg scale-105' 
                              : 'border-slate-200 hover:border-blue-300 hover:shadow-md'
                          }`}
                        >
                          <img
                            src={photo}
                            alt={`Thumbnail ${index + 1}`}
                            className="w-full h-full object-cover"
                          />
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Enhanced Location Map */}
            <div className="group bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 hover:shadow-2xl transition-all duration-300">
              <div className="p-8 border-b border-slate-100">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-gradient-to-r from-red-500 to-pink-500 rounded-lg">
                      <MapPin size={20} className="text-white" />
                    </div>
                    <h2 className="text-2xl font-bold text-slate-800">Location</h2>
                  </div>
                  <div className="flex gap-2">
                    <button className="p-2 bg-slate-100 hover:bg-slate-200 rounded-lg transition-colors">
                      <ZoomIn size={16} className="text-slate-600" />
                    </button>
                    <button className="p-2 bg-slate-100 hover:bg-slate-200 rounded-lg transition-colors">
                      <Navigation size={16} className="text-slate-600" />
                    </button>
                  </div>
                </div>
              </div>
              <div className="p-8">
                <div className="rounded-xl overflow-hidden shadow-lg">
                  <ReportMap reports={[report]} height="300px" />
                </div>
              </div>
            </div>

            {/* Enhanced Assistance Log */}
            <div className="group bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 hover:shadow-2xl transition-all duration-300">
              <div className="p-8 border-b border-slate-100">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg">
                    <Heart size={20} className="text-white" />
                  </div>
                  <h2 className="text-2xl font-bold text-slate-800">Assistance Provided</h2>
                </div>
              </div>
              <div className="p-8">
                {report.assistanceLog.length > 0 ? (
                  <div className="space-y-6">
                    {report.assistanceLog.map((entry) => (
                      <div key={entry.id} className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-6 border border-green-100 shadow-sm hover:shadow-md transition-shadow">
                        <div className="flex items-start space-x-4">
                          <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-full flex items-center justify-center flex-shrink-0 shadow-lg">
                            <Heart size={20} />
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center space-x-3 mb-3">
                              <span className="font-bold text-slate-800 text-lg">{entry.providerName}</span>
                              <span className="text-sm font-medium text-slate-500 bg-white/60 px-3 py-1 rounded-full">
                                {format(entry.createdAt, 'MMM d, yyyy')}
                              </span>
                              {entry.endorsed && (
                                <div className="inline-flex items-center gap-1 bg-green-100 text-green-800 text-xs px-3 py-1 rounded-full font-semibold">
                                  <CheckCircle size={12} />
                                  ✅ Endorsed
                                </div>
                              )}
                            </div>
                            <div className="bg-white/60 backdrop-blur-sm p-4 rounded-lg border border-white/20">
                              <p className="text-slate-700 font-medium leading-relaxed">{entry.description}</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12 bg-gradient-to-r from-slate-50 to-gray-50 rounded-xl border border-slate-100">
                    <div className="p-4 bg-white/60 backdrop-blur-sm rounded-xl border border-white/20 inline-block">
                      <Heart size={48} className="text-slate-400 mx-auto mb-4" />
                      <p className="text-slate-600 font-semibold mb-2">No assistance has been provided yet.</p>
                      <p className="text-sm text-slate-500">Be the first to help this community! 🤝</p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Enhanced Sidebar */}
          <div className="space-y-8">
            {/* Enhanced Assistance Needed */}
            <div className="group bg-white/90 backdrop-blur-sm rounded-2xl shadow-xl border border-red-200/50 hover:shadow-2xl transition-all duration-300">
              <div className="p-8 bg-gradient-to-r from-red-50 to-pink-50 border-b border-red-100 rounded-t-2xl">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-gradient-to-r from-red-500 to-red-600 rounded-lg shadow-lg">
                    <AlertTriangle size={20} className="text-white" />
                  </div>
                  <h3 className="text-xl font-bold text-red-800">Assistance Needed</h3>
                </div>
              </div>
              <div className="p-8">
                <div className="space-y-6">
                  <div className="space-y-3">
                    {report.assistanceNeeded.map((need, index) => (
                      <div key={index} className="flex items-center gap-3 bg-white/60 backdrop-blur-sm p-3 rounded-lg border border-white/20">
                        <Clock size={16} className="text-red-500 flex-shrink-0" />
                        <span className="text-slate-700 font-medium">{need}</span>
                      </div>
                    ))}
                  </div>
                  <div className="bg-gradient-to-r from-slate-50 to-gray-50 rounded-xl p-6 border border-slate-100">
                    <h4 className="font-bold mb-3 text-slate-800 flex items-center gap-2">
                      <Info size={16} className="text-blue-500" />
                      Details
                    </h4>
                    <p className="text-slate-700 leading-relaxed font-medium">
                      {report.assistanceDescription}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Enhanced Action Buttons */}
            {user && (
              <div className="group bg-white/90 backdrop-blur-sm rounded-2xl shadow-xl border border-blue-200/50 hover:shadow-2xl transition-all duration-300">
                <div className="p-8 bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-blue-100 rounded-t-2xl">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg shadow-lg">
                      <Users size={20} className="text-white" />
                    </div>
                    <h3 className="text-xl font-bold text-blue-800">Take Action</h3>
                  </div>
                </div>
                <div className="p-8">
                  <div className="space-y-4">
                    <button
                      onClick={() => setShowAssistanceForm(!showAssistanceForm)}
                      className="w-full bg-gradient-to-r from-red-600 to-red-700 text-white py-4 px-6 rounded-xl font-bold hover:from-red-700 hover:to-red-800 transition-all duration-200 flex items-center justify-center shadow-lg hover:shadow-xl transform hover:scale-[1.02]"
                    >
                      <Heart size={20} className="mr-3" />
                      <span className="text-lg">💝 Offer Assistance</span>
                    </button>
                    <button className="w-full bg-gradient-to-r from-green-500 to-green-600 text-white py-4 px-6 rounded-xl font-bold hover:from-green-600 hover:to-green-700 transition-all duration-200 flex items-center justify-center shadow-lg hover:shadow-xl transform hover:scale-[1.02]">
                      <MessageCircle size={20} className="mr-3" />
                      <span className="text-lg">💬 Contact Reporter</span>
                    </button>
                  </div>

                  {showAssistanceForm && (
                    <div className="mt-8 p-6 bg-gradient-to-r from-slate-50 to-gray-50 rounded-xl border border-slate-100">
                      <label className="flex items-center gap-2 text-lg font-bold text-slate-800 mb-4">
                        <Package size={18} className="text-blue-500" />
                        Describe the assistance you can provide:
                      </label>
                      <textarea
                        value={assistanceText}
                        onChange={(e) => setAssistanceText(e.target.value)}
                        className="w-full px-4 py-3 border border-slate-300 rounded-xl focus:ring-2 focus:ring-red-500 focus:border-red-500 bg-white/80 backdrop-blur-sm font-medium"
                        rows={4}
                        placeholder="I can help with transportation, supplies, temporary shelter, etc..."
                      />
                      <div className="flex space-x-3 mt-4">
                        <button
                          onClick={handleOfferAssistance}
                          className="flex-1 bg-gradient-to-r from-red-600 to-red-700 text-white py-3 px-4 rounded-xl hover:from-red-700 hover:to-red-800 transition-all duration-200 font-bold shadow-lg hover:shadow-xl"
                        >
                          ✅ Submit Offer
                        </button>
                        <button
                          onClick={() => setShowAssistanceForm(false)}
                          className="flex-1 bg-gradient-to-r from-slate-400 to-slate-500 text-white py-3 px-4 rounded-xl hover:from-slate-500 hover:to-slate-600 transition-all duration-200 font-bold shadow-lg hover:shadow-xl"
                        >
                          ❌ Cancel
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

            {!user && (
              <div className="group bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-2xl p-8 text-center shadow-xl hover:shadow-2xl transition-all duration-300">
                <div className="bg-white/60 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                  <div className="p-3 bg-blue-100 rounded-full w-fit mx-auto mb-4">
                    <Heart size={24} className="text-blue-600" />
                  </div>
                  <h3 className="text-xl font-bold text-blue-900 mb-3">Want to Help? 🤝</h3>
                  <p className="text-blue-700 font-medium mb-6">
                    Log in to offer assistance and connect with this community.
                  </p>
                  <button className="bg-gradient-to-r from-blue-600 to-blue-700 text-white px-6 py-3 rounded-xl hover:from-blue-700 hover:to-blue-800 transition-all duration-200 font-bold shadow-lg hover:shadow-xl transform hover:scale-[1.02]">
                    🔐 Log In to Help
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ReportDetail;
