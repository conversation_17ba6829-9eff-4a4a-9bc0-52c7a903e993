import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link, useNavigate } from 'react-router-dom';
import { format } from 'date-fns';
import {
  ArrowLeft,
  MapPin,
  Calendar,
  User,
  CheckCircle,
  Clock,
  AlertTriangle,
  Share2,
  Flag,
  ExternalLink,
  Phone,
  Mail,
  Globe,
  Navigation,
  ZoomIn,
  ZoomOut,
  Eye,
  MessageSquare,
  Heart,
  Bookmark,
  Download,
  Camera,
  Shield,
  Activity,
  ChevronLeft
} from 'lucide-react';

// Components
import Header from '../components/Layout/Header';
import Footer from '../components/Layout/Footer';
import LoadingSpinner from '../components/Common/LoadingSpinner';

// Data
import { mockReports } from '../data/mockData';
import { Report } from '../types';

const ReportDetails: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [report, setReport] = useState<Report | null>(null);
  const [loading, setLoading] = useState(true);
  const [imageLoading, setImageLoading] = useState(true);
  const [relatedReports, setRelatedReports] = useState<Report[]>([]);

  useEffect(() => {
    // Simulate API call
    const fetchReport = async () => {
      setLoading(true);
      try {
        // In a real app, this would be an API call
        const foundReport = mockReports.find(r => r.id === id);
        if (foundReport) {
          setReport(foundReport);
          // Get related reports (same type, different id)
          const related = mockReports
            .filter(r => r.type === foundReport.type && r.id !== foundReport.id)
            .slice(0, 3);
          setRelatedReports(related);
        }
      } catch (error) {
        console.error('Error fetching report:', error);
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      fetchReport();
    }
  }, [id]);

  // Get severity color classes
  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'bg-red-500';
      case 'high':
        return 'bg-orange-500';
      case 'medium':
        return 'bg-yellow-500';
      case 'low':
        return 'bg-green-500';
      default:
        return 'bg-gray-500';
    }
  };

  // Get default image for report type
  const getDefaultImage = (type: string) => {
    const defaultImages = {
      flood: 'https://images.pexels.com/photos/1118873/pexels-photo-1118873.jpeg',
      fire: 'https://images.pexels.com/photos/1112080/pexels-photo-1112080.jpeg',
      earthquake: 'https://images.pexels.com/photos/2166711/pexels-photo-2166711.jpeg',
      storm: 'https://images.pexels.com/photos/1446076/pexels-photo-1446076.jpeg',
      default: 'https://images.pexels.com/photos/1446076/pexels-photo-1446076.jpeg'
    };
    return defaultImages[type as keyof typeof defaultImages] || defaultImages.default;
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-white">
        <Header />
        <div className="flex items-center justify-center min-h-[60vh]">
          <LoadingSpinner size="lg" />
        </div>
        <Footer />
      </div>
    );
  }

  if (!report) {
    return (
      <div className="min-h-screen bg-white">
        <Header />
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="text-center">
            <AlertTriangle size={64} className="text-gray-400 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Report Not Found</h2>
            <p className="text-gray-600 mb-6">The report you're looking for doesn't exist or has been removed.</p>
            <Link
              to="/"
              className="bg-blue-600 text-white px-6 py-3 rounded-xl font-semibold hover:bg-blue-700 transition-colors inline-flex items-center"
            >
              <ArrowLeft size={18} className="mr-2" />
              Back to Home
            </Link>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-slate-50">
      <Header />

      <main className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          {/* Back Navigation */}
          <div className="mb-6">
            <button
              onClick={() => navigate(-1)}
              className="flex items-center gap-2 text-slate-600 hover:text-slate-800 transition-colors"
            >
              <ChevronLeft size={20} />
              <span>Back to Reports</span>
            </button>
          </div>

          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center gap-2 mb-4">
              {report.verified && (
                <div className="inline-flex items-center gap-1 bg-green-100 text-green-800 border border-green-200 px-3 py-1 rounded-full text-sm font-medium">
                  <CheckCircle size={14} />
                  Verified Report
                </div>
              )}
              <div className="inline-flex items-center bg-slate-100 text-slate-700 border border-slate-200 px-3 py-1 rounded-full text-sm font-medium capitalize">
                {report.type}
              </div>
              <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-bold text-white ${getSeverityColor(report.severity)}`}>
                {report.severity?.toUpperCase()}
              </div>
            </div>

            <h1 className="text-3xl font-bold text-slate-800 mb-2">
              {report.type.charAt(0).toUpperCase() + report.type.slice(1)} Impact in {report.location}
            </h1>

            <div className="flex items-center gap-4 text-slate-600">
              <div className="flex items-center gap-1">
                <MapPin size={16} />
                {report.location}
              </div>
              <div className="flex items-center gap-1">
                <Calendar size={16} />
                Reported on {format(new Date(report.createdAt), 'MMMM d, yyyy')}
              </div>
              <div className="flex items-center gap-1">
                <User size={16} />
                {report.reportedBy}
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Main Content - Left Column */}
            <div className="lg:col-span-2 space-y-6">
              {/* Impact Photos */}
              <div className="bg-white rounded-xl shadow-sm border border-slate-200">
                <div className="p-6 border-b border-slate-200">
                  <h2 className="text-xl font-semibold text-slate-800">Impact Photos</h2>
                </div>
                <div className="p-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {report.images && report.images.length > 0 ? (
                      report.images.map((image, index) => (
                        <div key={index} className="aspect-video rounded-lg overflow-hidden">
                          <img
                            src={image}
                            alt={`Impact photo ${index + 1}`}
                            className="w-full h-full object-cover hover:scale-105 transition-transform cursor-pointer"
                          />
                        </div>
                      ))
                    ) : (
                      <div className="aspect-video rounded-lg overflow-hidden">
                        <img
                          src={getDefaultImage(report.type)}
                          alt="Default impact photo"
                          className="w-full h-full object-cover hover:scale-105 transition-transform cursor-pointer"
                        />
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Report Details */}
              <div className="bg-white rounded-xl shadow-sm border border-slate-200">
                <div className="p-6 border-b border-slate-200">
                  <h2 className="text-xl font-semibold text-slate-800">Report Details</h2>
                </div>
                <div className="p-6 space-y-4">
                  <div>
                    <h4 className="font-semibold mb-2 text-slate-800">Reporter's Description</h4>
                    <p className="text-slate-700 leading-relaxed">{report.description}</p>
                  </div>

                  <div className="border-t border-slate-200 pt-4">
                    <h4 className="font-semibold mb-2 text-slate-800">Report Information</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <span className="text-sm text-slate-500">Report ID</span>
                        <p className="font-mono text-sm bg-slate-100 px-3 py-2 rounded-lg mt-1">#{report.id}</p>
                      </div>
                      <div>
                        <span className="text-sm text-slate-500">Status</span>
                        <div className={`inline-flex items-center px-3 py-2 rounded-lg text-sm font-semibold mt-1 ${
                          report.status === 'verified' ? 'bg-green-100 text-green-700' :
                          report.status === 'pending' ? 'bg-yellow-100 text-yellow-700' :
                          'bg-slate-100 text-slate-700'
                        }`}>
                          {report.status === 'verified' && <CheckCircle size={16} className="mr-2" />}
                          {report.status === 'pending' && <Clock size={16} className="mr-2" />}
                          {report.status.charAt(0).toUpperCase() + report.status.slice(1)}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="border-t border-slate-200 pt-4">
                    <h4 className="font-semibold mb-2 text-slate-800">Coordinates</h4>
                    <div className="bg-blue-50 p-3 rounded-lg">
                      <p className="text-sm"><strong>Latitude:</strong> {report.coordinates.lat.toFixed(6)}</p>
                      <p className="text-sm"><strong>Longitude:</strong> {report.coordinates.lng.toFixed(6)}</p>
                      <p className="text-sm"><strong>Last Updated:</strong> {format(new Date(report.updatedAt), 'MMMM d, yyyy • h:mm a')}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Location Map */}
              <div className="bg-white rounded-xl shadow-sm border border-slate-200">
                <div className="p-6 border-b border-slate-200">
                  <h2 className="text-xl font-semibold text-slate-800">Location</h2>
                </div>
                <div className="p-6">
                  {/* Map Placeholder */}
                  <div className="bg-slate-100 rounded-lg h-64 flex items-center justify-center border-2 border-dashed border-slate-300 mb-4">
                    <div className="text-center">
                      <Navigation size={48} className="text-slate-400 mx-auto mb-2" />
                      <p className="text-slate-500 font-medium">Interactive Map</p>
                      <p className="text-slate-400 text-sm">Location: {report.location}</p>
                    </div>
                  </div>

                  {/* Map Controls */}
                  <div className="flex justify-center space-x-4">
                    <button className="bg-blue-100 text-blue-700 px-4 py-2 rounded-lg font-medium hover:bg-blue-200 transition-colors flex items-center">
                      <ZoomIn size={16} className="mr-2" />
                      Zoom In
                    </button>
                    <button className="bg-blue-100 text-blue-700 px-4 py-2 rounded-lg font-medium hover:bg-blue-200 transition-colors flex items-center">
                      <ZoomOut size={16} className="mr-2" />
                      Zoom Out
                    </button>
                    <button className="bg-green-100 text-green-700 px-4 py-2 rounded-lg font-medium hover:bg-green-200 transition-colors flex items-center">
                      <ExternalLink size={16} className="mr-2" />
                      Open in Maps
                    </button>
                  </div>

                  <p className="text-sm text-slate-600 mt-4 text-center">{report.location}</p>
                </div>
              </div>

              {/* Timeline */}
              <div className="bg-white rounded-xl shadow-sm border border-slate-200">
                <div className="p-6 border-b border-slate-200">
                  <h2 className="text-xl font-semibold text-slate-800">Report Timeline</h2>
                </div>
                <div className="p-6">
                  <div className="space-y-6">
                    {/* Timeline Item 1 */}
                    <div className="flex items-start space-x-4">
                      <div className="flex-shrink-0 w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                        <Flag size={20} className="text-blue-600" />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center justify-between mb-1">
                          <h3 className="font-semibold text-slate-800">Report Submitted</h3>
                          <span className="text-sm text-slate-500">
                            {format(new Date(report.createdAt), 'MMM d, yyyy • h:mm a')}
                          </span>
                        </div>
                        <p className="text-slate-600">Initial report submitted by {report.reportedBy}</p>
                      </div>
                    </div>

                    {/* Timeline Item 2 */}
                    {report.verified && (
                      <div className="flex items-start space-x-4">
                        <div className="flex-shrink-0 w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                          <CheckCircle size={20} className="text-green-600" />
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center justify-between mb-1">
                            <h3 className="font-semibold text-slate-800">Report Verified</h3>
                            <span className="text-sm text-slate-500">
                              {format(new Date(report.updatedAt), 'MMM d, yyyy • h:mm a')}
                            </span>
                          </div>
                          <p className="text-slate-600">Report verified by emergency response team</p>
                        </div>
                      </div>
                    )}

                    {/* Timeline Item 3 */}
                    <div className="flex items-start space-x-4">
                      <div className="flex-shrink-0 w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center">
                        <Eye size={20} className="text-yellow-600" />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center justify-between mb-1">
                          <h3 className="font-semibold text-slate-800">Under Investigation</h3>
                          <span className="text-sm text-slate-500">Current Status</span>
                        </div>
                        <p className="text-slate-600">Emergency teams are assessing the situation</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Related Reports */}
              {relatedReports.length > 0 && (
                <div className="bg-white rounded-xl shadow-sm border border-slate-200">
                  <div className="p-6 border-b border-slate-200">
                    <h2 className="text-xl font-semibold text-slate-800">
                      Related {report.type.charAt(0).toUpperCase() + report.type.slice(1)} Reports
                    </h2>
                  </div>
                  <div className="p-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {relatedReports.map((relatedReport) => (
                        <Link
                          key={relatedReport.id}
                          to={`/reports/${relatedReport.id}`}
                          className="group bg-slate-50 rounded-lg p-4 hover:bg-slate-100 transition-colors border border-slate-200 hover:border-blue-300"
                        >
                          <div className="flex items-start space-x-3">
                            <img
                              src={relatedReport.images?.[0] || getDefaultImage(relatedReport.type)}
                              alt={relatedReport.title}
                              className="w-12 h-12 rounded-lg object-cover"
                            />
                            <div className="flex-1 min-w-0">
                              <h3 className="font-medium text-slate-800 group-hover:text-blue-600 transition-colors line-clamp-2 text-sm">
                                {relatedReport.title}
                              </h3>
                              <p className="text-xs text-slate-600 mt-1">{relatedReport.location}</p>
                              <div className="flex items-center space-x-2 mt-2">
                                <span className={`px-2 py-1 rounded-full text-xs font-medium text-white ${getSeverityColor(relatedReport.severity)}`}>
                                  {relatedReport.severity}
                                </span>
                                <span className="text-xs text-slate-500">
                                  {format(new Date(relatedReport.createdAt), 'MMM d')}
                                </span>
                              </div>
                            </div>
                          </div>
                        </Link>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Right Sidebar - Assistance Panel */}
            <div className="space-y-6">
              {/* Assistance Needed */}
              <div className="bg-white rounded-xl shadow-sm border border-red-200">
                <div className="p-6 bg-red-50 border-b border-red-200 rounded-t-xl">
                  <h2 className="text-xl font-semibold text-red-800">Assistance Needed</h2>
                </div>
                <div className="p-6">
                  <div className="space-y-4">
                    <div>
                      <h4 className="font-semibold mb-2 text-slate-800">Required Support</h4>
                      <div className="flex flex-wrap gap-2">
                        {/* Mock assistance needed based on report type */}
                        {report.type === 'flood' && (
                          <>
                            <span className="inline-flex items-center border border-red-300 text-red-700 px-3 py-1 rounded-full text-sm">
                              Emergency Shelter
                            </span>
                            <span className="inline-flex items-center border border-red-300 text-red-700 px-3 py-1 rounded-full text-sm">
                              Food & Water
                            </span>
                            <span className="inline-flex items-center border border-red-300 text-red-700 px-3 py-1 rounded-full text-sm">
                              Medical Supplies
                            </span>
                          </>
                        )}
                        {report.type === 'fire' && (
                          <>
                            <span className="inline-flex items-center border border-red-300 text-red-700 px-3 py-1 rounded-full text-sm">
                              Evacuation Support
                            </span>
                            <span className="inline-flex items-center border border-red-300 text-red-700 px-3 py-1 rounded-full text-sm">
                              Temporary Housing
                            </span>
                            <span className="inline-flex items-center border border-red-300 text-red-700 px-3 py-1 rounded-full text-sm">
                              Emergency Supplies
                            </span>
                          </>
                        )}
                        {(report.type === 'earthquake' || report.type === 'storm') && (
                          <>
                            <span className="inline-flex items-center border border-red-300 text-red-700 px-3 py-1 rounded-full text-sm">
                              Search & Rescue
                            </span>
                            <span className="inline-flex items-center border border-red-300 text-red-700 px-3 py-1 rounded-full text-sm">
                              Medical Aid
                            </span>
                            <span className="inline-flex items-center border border-red-300 text-red-700 px-3 py-1 rounded-full text-sm">
                              Cleanup Volunteers
                            </span>
                          </>
                        )}
                      </div>
                    </div>

                    <div>
                      <h4 className="font-semibold mb-2 text-slate-800">Details</h4>
                      <p className="text-sm text-slate-700">
                        {report.type === 'flood' && "Urgent need for emergency shelter and clean water supplies. Multiple families displaced and require immediate assistance."}
                        {report.type === 'fire' && "Immediate evacuation support needed. Residents require temporary housing and emergency supplies."}
                        {(report.type === 'earthquake' || report.type === 'storm') && "Emergency response teams needed for search and rescue operations. Medical aid and cleanup volunteers required."}
                      </p>
                    </div>

                    <button className="w-full bg-red-600 text-white py-3 px-4 rounded-lg font-semibold hover:bg-red-700 transition-colors flex items-center justify-center">
                      <Heart size={18} className="mr-2" />
                      Offer Assistance
                    </button>
                  </div>
                </div>
              </div>

              {/* Assistance Provided */}
              <div className="bg-white rounded-xl shadow-sm border border-slate-200">
                <div className="p-6 border-b border-slate-200">
                  <h2 className="text-xl font-semibold text-slate-800">Assistance Provided</h2>
                </div>
                <div className="p-6">
                  <div className="space-y-4">
                    {/* Mock assistance provided */}
                    <div className="border-l-4 border-green-500 pl-4 py-2">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <p className="font-medium text-sm text-slate-800">Red Cross Emergency Response</p>
                            <div className="inline-flex items-center gap-1 bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                              <CheckCircle size={12} />
                              Endorsed
                            </div>
                          </div>
                          <p className="text-xs text-slate-600 mb-1">Emergency Shelter</p>
                          <p className="text-sm text-slate-700">Provided emergency shelter for 12 families at the community center</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-1 mt-2 text-xs text-slate-500">
                        <Clock size={12} />
                        {format(new Date(report.updatedAt), 'MMMM d, yyyy')}
                      </div>
                    </div>

                    <div className="border-l-4 border-green-500 pl-4 py-2">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <p className="font-medium text-sm text-slate-800">Local Food Bank</p>
                            <div className="inline-flex items-center gap-1 bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                              <CheckCircle size={12} />
                              Endorsed
                            </div>
                          </div>
                          <p className="text-xs text-slate-600 mb-1">Food & Water</p>
                          <p className="text-sm text-slate-700">Delivered 500 meals and 200 water bottles to affected area</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-1 mt-2 text-xs text-slate-500">
                        <Clock size={12} />
                        {format(new Date(report.createdAt), 'MMMM d, yyyy')}
                      </div>
                    </div>

                    <div className="border-l-4 border-blue-500 pl-4 py-2">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <p className="font-medium text-sm text-slate-800">Community Volunteers</p>
                          </div>
                          <p className="text-xs text-slate-600 mb-1">Volunteer Support</p>
                          <p className="text-sm text-slate-700">20 volunteers helped with cleanup and moving belongings</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-1 mt-2 text-xs text-slate-500">
                        <Clock size={12} />
                        {format(new Date(report.createdAt), 'MMMM d, yyyy')}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Emergency Contacts */}
              <div className="bg-white rounded-xl shadow-sm border border-red-200">
                <div className="p-6 bg-red-50 border-b border-red-200 rounded-t-xl">
                  <h2 className="text-xl font-semibold text-red-800 flex items-center">
                    <Shield size={20} className="mr-2" />
                    Emergency Contacts
                  </h2>
                </div>
                <div className="p-6">
                  <div className="space-y-3">
                    <a
                      href="tel:911"
                      className="w-full bg-red-600 text-white py-3 px-4 rounded-lg font-semibold hover:bg-red-700 transition-colors flex items-center justify-center"
                    >
                      <Phone size={18} className="mr-2" />
                      Call 911
                    </a>

                    <a
                      href="mailto:<EMAIL>"
                      className="w-full bg-white text-slate-700 py-3 px-4 rounded-lg font-semibold hover:bg-slate-50 transition-colors flex items-center justify-center border border-slate-200"
                    >
                      <Mail size={18} className="mr-2" />
                      Email Emergency Team
                    </a>

                    <a
                      href="https://emergency.gov"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="w-full bg-white text-slate-700 py-3 px-4 rounded-lg font-semibold hover:bg-slate-50 transition-colors flex items-center justify-center border border-slate-200"
                    >
                      <Globe size={18} className="mr-2" />
                      Emergency Resources
                    </a>
                  </div>
                </div>
              </div>

              {/* Community Engagement */}
              <div className="bg-white rounded-xl shadow-sm border border-blue-200">
                <div className="p-6 bg-blue-50 border-b border-blue-200 rounded-t-xl">
                  <h2 className="text-xl font-semibold text-blue-800">Community</h2>
                </div>
                <div className="p-6">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-slate-700">Views</span>
                      <div className="flex items-center space-x-1">
                        <Eye size={16} className="text-slate-500" />
                        <span className="font-semibold">1,247</span>
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-slate-700">Helpful</span>
                      <div className="flex items-center space-x-1">
                        <Heart size={16} className="text-red-500" />
                        <span className="font-semibold">89</span>
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-slate-700">Comments</span>
                      <div className="flex items-center space-x-1">
                        <MessageSquare size={16} className="text-blue-500" />
                        <span className="font-semibold">23</span>
                      </div>
                    </div>

                    <div className="pt-4 border-t border-blue-200">
                      <button className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-semibold hover:bg-blue-700 transition-colors flex items-center justify-center">
                        <MessageSquare size={18} className="mr-2" />
                        Join Discussion
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="bg-white rounded-xl shadow-sm border border-slate-200">
                <div className="p-6 border-b border-slate-200">
                  <h2 className="text-xl font-semibold text-slate-800">Actions</h2>
                </div>
                <div className="p-6">
                  <div className="space-y-3">
                    <button className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-semibold hover:bg-blue-700 transition-colors flex items-center justify-center">
                      <Share2 size={18} className="mr-2" />
                      Share Report
                    </button>

                    <button className="w-full bg-slate-100 text-slate-700 py-3 px-4 rounded-lg font-semibold hover:bg-slate-200 transition-colors flex items-center justify-center">
                      <Bookmark size={18} className="mr-2" />
                      Save Report
                    </button>

                    <button className="w-full bg-slate-100 text-slate-700 py-3 px-4 rounded-lg font-semibold hover:bg-slate-200 transition-colors flex items-center justify-center">
                      <Download size={18} className="mr-2" />
                      Download PDF
                    </button>

                    <button className="w-full bg-red-100 text-red-700 py-3 px-4 rounded-lg font-semibold hover:bg-red-200 transition-colors flex items-center justify-center">
                      <Flag size={18} className="mr-2" />
                      Report Issue
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default ReportDetails;
