import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link, useNavigate } from 'react-router-dom';
import { format } from 'date-fns';
import {
  ArrowLeft,
  MapPin,
  Calendar,
  User,
  CheckCircle,
  Clock,
  AlertTriangle,
  Share2,
  Flag,
  ExternalLink,
  Phone,
  Mail,
  Globe,
  Navigation,
  ZoomIn,
  ZoomOut,
  Eye,
  MessageSquare,
  Heart,
  Bookmark,
  Download,
  Camera,
  Shield,
  Activity
} from 'lucide-react';

// Components
import Header from '../components/Layout/Header';
import Footer from '../components/Layout/Footer';
import LoadingSpinner from '../components/Common/LoadingSpinner';

// Data
import { mockReports } from '../data/mockData';
import { Report } from '../types';

const ReportDetails: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [report, setReport] = useState<Report | null>(null);
  const [loading, setLoading] = useState(true);
  const [imageLoading, setImageLoading] = useState(true);
  const [relatedReports, setRelatedReports] = useState<Report[]>([]);

  useEffect(() => {
    // Simulate API call
    const fetchReport = async () => {
      setLoading(true);
      try {
        // In a real app, this would be an API call
        const foundReport = mockReports.find(r => r.id === id);
        if (foundReport) {
          setReport(foundReport);
          // Get related reports (same type, different id)
          const related = mockReports
            .filter(r => r.type === foundReport.type && r.id !== foundReport.id)
            .slice(0, 3);
          setRelatedReports(related);
        }
      } catch (error) {
        console.error('Error fetching report:', error);
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      fetchReport();
    }
  }, [id]);

  // Get severity color classes
  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'bg-red-500';
      case 'high':
        return 'bg-orange-500';
      case 'medium':
        return 'bg-yellow-500';
      case 'low':
        return 'bg-green-500';
      default:
        return 'bg-gray-500';
    }
  };

  // Get default image for report type
  const getDefaultImage = (type: string) => {
    const defaultImages = {
      flood: 'https://images.pexels.com/photos/1118873/pexels-photo-1118873.jpeg',
      fire: 'https://images.pexels.com/photos/1112080/pexels-photo-1112080.jpeg',
      earthquake: 'https://images.pexels.com/photos/2166711/pexels-photo-2166711.jpeg',
      storm: 'https://images.pexels.com/photos/1446076/pexels-photo-1446076.jpeg',
      default: 'https://images.pexels.com/photos/1446076/pexels-photo-1446076.jpeg'
    };
    return defaultImages[type as keyof typeof defaultImages] || defaultImages.default;
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-white">
        <Header />
        <div className="flex items-center justify-center min-h-[60vh]">
          <LoadingSpinner size="lg" />
        </div>
        <Footer />
      </div>
    );
  }

  if (!report) {
    return (
      <div className="min-h-screen bg-white">
        <Header />
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="text-center">
            <AlertTriangle size={64} className="text-gray-400 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Report Not Found</h2>
            <p className="text-gray-600 mb-6">The report you're looking for doesn't exist or has been removed.</p>
            <Link
              to="/"
              className="bg-blue-600 text-white px-6 py-3 rounded-xl font-semibold hover:bg-blue-700 transition-colors inline-flex items-center"
            >
              <ArrowLeft size={18} className="mr-2" />
              Back to Home
            </Link>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      {/* Breadcrumb Navigation */}
      <nav className="bg-white border-b border-gray-200 py-4">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center space-x-2 text-sm">
            <Link to="/" className="text-blue-600 hover:text-blue-800 font-medium">
              Home
            </Link>
            <span className="text-gray-400">/</span>
            <span className="text-gray-600">Reports</span>
            <span className="text-gray-400">/</span>
            <span className="text-gray-900 font-medium truncate">
              {report.title}
            </span>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="relative">
        {/* Hero Image */}
        <div className="relative h-[50vh] sm:h-[60vh] md:h-[70vh] overflow-hidden">
          {imageLoading && (
            <div className="absolute inset-0 bg-gray-200 animate-pulse flex items-center justify-center">
              <Camera size={48} className="text-gray-400" />
            </div>
          )}
          <img
            src={report.images?.[0] || getDefaultImage(report.type)}
            alt={report.title}
            className={`w-full h-full object-cover transition-opacity duration-500 ${
              imageLoading ? 'opacity-0' : 'opacity-100'
            }`}
            onLoad={() => setImageLoading(false)}
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent"></div>
          
          {/* Back Button */}
          <button
            onClick={() => navigate(-1)}
            className="absolute top-6 left-6 bg-white/20 backdrop-blur-md text-white p-3 rounded-full hover:bg-white/30 transition-colors border border-white/30"
          >
            <ArrowLeft size={24} />
          </button>

          {/* Share Button */}
          <button className="absolute top-6 right-6 bg-white/20 backdrop-blur-md text-white p-3 rounded-full hover:bg-white/30 transition-colors border border-white/30">
            <Share2 size={24} />
          </button>

          {/* Hero Content */}
          <div className="absolute bottom-0 left-0 right-0 p-4 sm:p-6 md:p-12">
            <div className="max-w-4xl">
              {/* Badges */}
              <div className="flex flex-wrap items-center gap-2 sm:gap-3 mb-4 sm:mb-6">
                <div className={`px-4 py-2 rounded-full text-sm font-bold text-white ${getSeverityColor(report.severity)} backdrop-blur-sm shadow-lg`}>
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
                    <span>{report.severity?.toUpperCase()} ALERT</span>
                  </div>
                </div>
                
                <div className="bg-blue-600/90 backdrop-blur-sm text-white px-4 py-2 rounded-full text-sm font-semibold shadow-lg capitalize">
                  {report.type} Emergency
                </div>
                
                {report.verified && (
                  <div className="bg-green-500/90 backdrop-blur-sm text-white px-4 py-2 rounded-full text-sm font-semibold flex items-center shadow-lg">
                    <CheckCircle size={16} className="mr-2" />
                    Verified Report
                  </div>
                )}
              </div>

              {/* Title */}
              <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-6xl font-bold text-white mb-3 sm:mb-4 leading-tight">
                {report.title}
              </h1>

              {/* Meta Info */}
              <div className="flex flex-wrap items-center gap-3 sm:gap-4 md:gap-6 text-white/90 text-sm sm:text-base">
                <div className="flex items-center space-x-2">
                  <MapPin size={20} />
                  <span className="font-medium">{report.location}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Calendar size={20} />
                  <span>{format(new Date(report.createdAt), 'MMMM d, yyyy • h:mm a')}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <User size={20} />
                  <span>Reported by {report.reportedBy}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
            {/* Main Content Column */}
            <div className="lg:col-span-2 space-y-8">
              {/* Report Description */}
              <div className="bg-white rounded-3xl shadow-lg p-8 border border-gray-100">
                <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                  <MessageSquare size={24} className="mr-3 text-blue-600" />
                  Report Description
                </h2>
                <p className="text-gray-700 text-lg leading-relaxed">
                  {report.description}
                </p>
              </div>

              {/* Location & Map Section */}
              <div className="bg-white rounded-3xl shadow-lg p-8 border border-gray-100">
                <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                  <MapPin size={24} className="mr-3 text-blue-600" />
                  Location Details
                </h2>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-2">Address</h3>
                    <p className="text-gray-700">{report.location}</p>
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-2">Coordinates</h3>
                    <p className="text-gray-700">
                      {report.coordinates.lat.toFixed(6)}, {report.coordinates.lng.toFixed(6)}
                    </p>
                  </div>
                </div>

                {/* Map Placeholder */}
                <div className="bg-gray-100 rounded-2xl h-64 flex items-center justify-center border-2 border-dashed border-gray-300">
                  <div className="text-center">
                    <Navigation size={48} className="text-gray-400 mx-auto mb-2" />
                    <p className="text-gray-500 font-medium">Interactive Map</p>
                    <p className="text-gray-400 text-sm">Location: {report.location}</p>
                  </div>
                </div>

                {/* Map Controls */}
                <div className="flex justify-center space-x-4 mt-4">
                  <button className="bg-blue-100 text-blue-700 px-4 py-2 rounded-lg font-medium hover:bg-blue-200 transition-colors flex items-center">
                    <ZoomIn size={16} className="mr-2" />
                    Zoom In
                  </button>
                  <button className="bg-blue-100 text-blue-700 px-4 py-2 rounded-lg font-medium hover:bg-blue-200 transition-colors flex items-center">
                    <ZoomOut size={16} className="mr-2" />
                    Zoom Out
                  </button>
                  <button className="bg-green-100 text-green-700 px-4 py-2 rounded-lg font-medium hover:bg-green-200 transition-colors flex items-center">
                    <ExternalLink size={16} className="mr-2" />
                    Open in Maps
                  </button>
                </div>
              </div>

              {/* Timeline Section */}
              <div className="bg-white rounded-3xl shadow-lg p-8 border border-gray-100">
                <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                  <Activity size={24} className="mr-3 text-blue-600" />
                  Report Timeline
                </h2>

                <div className="space-y-6">
                  {/* Timeline Item 1 */}
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0 w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                      <Flag size={20} className="text-blue-600" />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-1">
                        <h3 className="font-semibold text-gray-900">Report Submitted</h3>
                        <span className="text-sm text-gray-500">
                          {format(new Date(report.createdAt), 'MMM d, yyyy • h:mm a')}
                        </span>
                      </div>
                      <p className="text-gray-600">Initial report submitted by {report.reportedBy}</p>
                    </div>
                  </div>

                  {/* Timeline Item 2 */}
                  {report.verified && (
                    <div className="flex items-start space-x-4">
                      <div className="flex-shrink-0 w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                        <CheckCircle size={20} className="text-green-600" />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center justify-between mb-1">
                          <h3 className="font-semibold text-gray-900">Report Verified</h3>
                          <span className="text-sm text-gray-500">
                            {format(new Date(report.updatedAt), 'MMM d, yyyy • h:mm a')}
                          </span>
                        </div>
                        <p className="text-gray-600">Report verified by emergency response team</p>
                      </div>
                    </div>
                  )}

                  {/* Timeline Item 3 */}
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0 w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center">
                      <Eye size={20} className="text-yellow-600" />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-1">
                        <h3 className="font-semibold text-gray-900">Under Investigation</h3>
                        <span className="text-sm text-gray-500">Current Status</span>
                      </div>
                      <p className="text-gray-600">Emergency teams are assessing the situation</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Related Reports */}
              {relatedReports.length > 0 && (
                <div className="bg-white rounded-3xl shadow-lg p-8 border border-gray-100">
                  <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                    <AlertTriangle size={24} className="mr-3 text-blue-600" />
                    Related {report.type.charAt(0).toUpperCase() + report.type.slice(1)} Reports
                  </h2>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {relatedReports.map((relatedReport) => (
                      <Link
                        key={relatedReport.id}
                        to={`/reports/${relatedReport.id}`}
                        className="group bg-gray-50 rounded-2xl p-6 hover:bg-gray-100 transition-colors border border-gray-200 hover:border-blue-300"
                      >
                        <div className="flex items-start space-x-4">
                          <img
                            src={relatedReport.images?.[0] || getDefaultImage(relatedReport.type)}
                            alt={relatedReport.title}
                            className="w-16 h-16 rounded-xl object-cover"
                          />
                          <div className="flex-1">
                            <h3 className="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors line-clamp-2">
                              {relatedReport.title}
                            </h3>
                            <p className="text-sm text-gray-600 mt-1">{relatedReport.location}</p>
                            <div className="flex items-center space-x-2 mt-2">
                              <span className={`px-2 py-1 rounded-full text-xs font-medium text-white ${getSeverityColor(relatedReport.severity)}`}>
                                {relatedReport.severity}
                              </span>
                              <span className="text-xs text-gray-500">
                                {format(new Date(relatedReport.createdAt), 'MMM d')}
                              </span>
                            </div>
                          </div>
                        </div>
                      </Link>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Report Status Card */}
              <div className="bg-white rounded-3xl shadow-lg p-6 border border-gray-100">
                <h3 className="text-lg font-bold text-gray-900 mb-4">Report Status</h3>

                <div className="space-y-4">
                  <div>
                    <div className="text-sm text-gray-500 mb-1">Current Status</div>
                    <div className={`inline-flex items-center px-3 py-2 rounded-full text-sm font-semibold ${
                      report.status === 'verified' ? 'bg-green-100 text-green-700' :
                      report.status === 'pending' ? 'bg-yellow-100 text-yellow-700' :
                      'bg-gray-100 text-gray-700'
                    }`}>
                      {report.status === 'verified' && <CheckCircle size={16} className="mr-2" />}
                      {report.status === 'pending' && <Clock size={16} className="mr-2" />}
                      {report.status.charAt(0).toUpperCase() + report.status.slice(1)}
                    </div>
                  </div>

                  <div>
                    <div className="text-sm text-gray-500 mb-1">Severity Level</div>
                    <div className={`inline-flex items-center px-3 py-2 rounded-full text-sm font-bold text-white ${getSeverityColor(report.severity)}`}>
                      <AlertTriangle size={16} className="mr-2" />
                      {report.severity?.toUpperCase()}
                    </div>
                  </div>

                  <div>
                    <div className="text-sm text-gray-500 mb-1">Report ID</div>
                    <div className="font-mono text-sm bg-gray-100 px-3 py-2 rounded-lg">
                      #{report.id}
                    </div>
                  </div>

                  <div>
                    <div className="text-sm text-gray-500 mb-1">Last Updated</div>
                    <div className="text-sm font-medium text-gray-900">
                      {format(new Date(report.updatedAt), 'MMM d, yyyy • h:mm a')}
                    </div>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="bg-white rounded-3xl shadow-lg p-6 border border-gray-100">
                <h3 className="text-lg font-bold text-gray-900 mb-4">Actions</h3>

                <div className="space-y-3">
                  <button className="w-full bg-blue-600 text-white py-3 px-4 rounded-xl font-semibold hover:bg-blue-700 transition-colors flex items-center justify-center">
                    <Share2 size={18} className="mr-2" />
                    Share Report
                  </button>

                  <button className="w-full bg-gray-100 text-gray-700 py-3 px-4 rounded-xl font-semibold hover:bg-gray-200 transition-colors flex items-center justify-center">
                    <Bookmark size={18} className="mr-2" />
                    Save Report
                  </button>

                  <button className="w-full bg-gray-100 text-gray-700 py-3 px-4 rounded-xl font-semibold hover:bg-gray-200 transition-colors flex items-center justify-center">
                    <Download size={18} className="mr-2" />
                    Download PDF
                  </button>

                  <button className="w-full bg-red-100 text-red-700 py-3 px-4 rounded-xl font-semibold hover:bg-red-200 transition-colors flex items-center justify-center">
                    <Flag size={18} className="mr-2" />
                    Report Issue
                  </button>
                </div>
              </div>

              {/* Emergency Contacts */}
              <div className="bg-gradient-to-br from-red-50 to-orange-50 rounded-3xl shadow-lg p-6 border border-red-100">
                <h3 className="text-lg font-bold text-gray-900 mb-4 flex items-center">
                  <Shield size={20} className="mr-2 text-red-600" />
                  Emergency Contacts
                </h3>

                <div className="space-y-3">
                  <a
                    href="tel:911"
                    className="w-full bg-red-600 text-white py-3 px-4 rounded-xl font-semibold hover:bg-red-700 transition-colors flex items-center justify-center"
                  >
                    <Phone size={18} className="mr-2" />
                    Call 911
                  </a>

                  <a
                    href="mailto:<EMAIL>"
                    className="w-full bg-white text-gray-700 py-3 px-4 rounded-xl font-semibold hover:bg-gray-50 transition-colors flex items-center justify-center border border-gray-200"
                  >
                    <Mail size={18} className="mr-2" />
                    Email Emergency Team
                  </a>

                  <a
                    href="https://emergency.gov"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="w-full bg-white text-gray-700 py-3 px-4 rounded-xl font-semibold hover:bg-gray-50 transition-colors flex items-center justify-center border border-gray-200"
                  >
                    <Globe size={18} className="mr-2" />
                    Emergency Resources
                  </a>
                </div>
              </div>

              {/* Community Engagement */}
              <div className="bg-gradient-to-br from-blue-50 to-purple-50 rounded-3xl shadow-lg p-6 border border-blue-100">
                <h3 className="text-lg font-bold text-gray-900 mb-4">Community</h3>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-700">Views</span>
                    <div className="flex items-center space-x-1">
                      <Eye size={16} className="text-gray-500" />
                      <span className="font-semibold">1,247</span>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-gray-700">Helpful</span>
                    <div className="flex items-center space-x-1">
                      <Heart size={16} className="text-red-500" />
                      <span className="font-semibold">89</span>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-gray-700">Comments</span>
                    <div className="flex items-center space-x-1">
                      <MessageSquare size={16} className="text-blue-500" />
                      <span className="font-semibold">23</span>
                    </div>
                  </div>
                </div>

                <div className="mt-4 pt-4 border-t border-blue-200">
                  <button className="w-full bg-blue-600 text-white py-3 px-4 rounded-xl font-semibold hover:bg-blue-700 transition-colors flex items-center justify-center">
                    <MessageSquare size={18} className="mr-2" />
                    Join Discussion
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Floating Action Button for Mobile */}
      <div className="fixed bottom-6 right-6 lg:hidden z-50">
        <button className="bg-red-600 text-white p-4 rounded-full shadow-2xl hover:bg-red-700 transition-colors">
          <Phone size={24} />
        </button>
      </div>

      <Footer />
    </div>
  );
};

export default ReportDetails;
