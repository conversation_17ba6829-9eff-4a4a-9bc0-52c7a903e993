import React, { useState, useRef, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { ShieldCheck, Menu, X, ChevronDown, AlertTriangle, Flame, Waves, Zap, Wind, MapPin } from 'lucide-react';

interface NavItem {
  name: string;
  path: string;
}

const Header: React.FC = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isReportDropdownOpen, setIsReportDropdownOpen] = useState(false);
  const location = useLocation();
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsReportDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const reportTypes = [
    {
      name: 'Fire Emergency',
      description: 'Report fires, smoke, or burning incidents',
      path: '/report/new?type=fire',
      icon: Flame,
      color: 'text-red-500',
      bgColor: 'bg-red-50'
    },
    {
      name: 'Flood Alert',
      description: 'Report flooding or water-related emergencies',
      path: '/report/new?type=flood',
      icon: Waves,
      color: 'text-blue-500',
      bgColor: 'bg-blue-50'
    },
    {
      name: 'Earthquake',
      description: 'Report seismic activity or structural damage',
      path: '/report/new?type=earthquake',
      icon: Zap,
      color: 'text-yellow-500',
      bgColor: 'bg-yellow-50'
    },
    {
      name: 'Storm Warning',
      description: 'Report severe weather conditions',
      path: '/report/new?type=storm',
      icon: Wind,
      color: 'text-purple-500',
      bgColor: 'bg-purple-50'
    },
    {
      name: 'General Emergency',
      description: 'Report other types of emergencies',
      path: '/report/new?type=other',
      icon: AlertTriangle,
      color: 'text-orange-500',
      bgColor: 'bg-orange-50'
    },
    {
      name: 'Location Report',
      description: 'Report incidents at specific locations',
      path: '/report/new?type=location',
      icon: MapPin,
      color: 'text-green-500',
      bgColor: 'bg-green-50'
    }
  ];

  const getNavItems = (): NavItem[] => {
    // Basic navigation items - can be extended based on user roles
    return [
      { name: 'Home', path: '/' },
      { name: 'View Reports', path: '/reports' },
      { name: 'Dashboard', path: '/dashboard' },
    ];
  };

  const isActivePage = (path: string): boolean => {
    return location.pathname === path;
  };

  return (
    <header className="bg-white shadow-sm border-b border-gray-100 sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link to="/" className="flex items-center space-x-3">
            <div className="p-2 bg-gradient-to-br from-blue-500 to-blue-600 text-white rounded-xl shadow-sm">
              <ShieldCheck size={24} />
            </div>
            <div>
              <h1 className="text-xl font-bold text-gray-900">DisasterWatch</h1>
              <p className="text-xs text-gray-500 -mt-1">Community Reporting</p>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            {getNavItems().map((item) => (
              <Link
                key={item.path}
                to={item.path}
                className={`text-gray-600 hover:text-blue-600 font-medium transition-colors ${
                  isActivePage(item.path) ? 'text-blue-600' : ''
                }`}
              >
                {item.name}
              </Link>
            ))}

            {/* Report Dropdown */}
            <div className="relative" ref={dropdownRef}>
              <button
                onClick={() => setIsReportDropdownOpen(!isReportDropdownOpen)}
                className="flex items-center space-x-1 text-gray-600 hover:text-blue-600 font-medium transition-colors"
              >
                <span>Report</span>
                <ChevronDown
                  size={16}
                  className={`transition-transform duration-200 ${
                    isReportDropdownOpen ? 'rotate-180' : ''
                  }`}
                />
              </button>

              {/* Dropdown Menu */}
              {isReportDropdownOpen && (
                <div className="absolute top-full left-0 mt-2 w-80 bg-white rounded-xl shadow-lg border border-gray-200 py-2 z-50">
                  <div className="px-4 py-2 border-b border-gray-100">
                    <h3 className="text-sm font-semibold text-gray-900">Report Emergency</h3>
                    <p className="text-xs text-gray-500">Choose the type of emergency to report</p>
                  </div>

                  <div className="py-2">
                    {reportTypes.map((report, index) => (
                      <Link
                        key={index}
                        to={report.path}
                        onClick={() => setIsReportDropdownOpen(false)}
                        className="flex items-center px-4 py-3 hover:bg-gray-50 transition-colors group"
                      >
                        <div className={`p-2 rounded-lg ${report.bgColor} mr-3 group-hover:scale-110 transition-transform`}>
                          <report.icon size={16} className={report.color} />
                        </div>
                        <div className="flex-1">
                          <div className="text-sm font-medium text-gray-900">{report.name}</div>
                          <div className="text-xs text-gray-500">{report.description}</div>
                        </div>
                      </Link>
                    ))}
                  </div>

                  <div className="px-4 py-2 border-t border-gray-100">
                    <Link
                      to="/reports"
                      onClick={() => setIsReportDropdownOpen(false)}
                      className="text-xs text-blue-600 hover:text-blue-700 font-medium"
                    >
                      View all reports →
                    </Link>
                  </div>
                </div>
              )}
            </div>
          </nav>

          {/* Desktop CTA Button */}
          <div className="hidden md:flex items-center space-x-4">
            <Link
              to="/report/new"
              className="bg-gradient-to-r from-red-500 to-orange-500 text-white px-6 py-2 rounded-lg hover:from-red-600 hover:to-orange-600 transition-all duration-200 font-semibold text-sm"
            >
              Report Emergency
            </Link>
          </div>

          {/* Mobile Menu Button */}
          <button
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className="md:hidden p-2 rounded-lg text-gray-600 hover:text-blue-600 hover:bg-gray-50 transition-colors"
          >
            {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </button>
        </div>
      </div>

      {/* Mobile Navigation Menu */}
      {isMenuOpen && (
        <div className="md:hidden bg-white border-t border-gray-100 shadow-lg">
          <div className="px-4 py-6 space-y-4">
            {getNavItems().map((item) => (
              <Link
                key={item.path}
                to={item.path}
                className={`block text-lg font-medium transition-colors ${
                  isActivePage(item.path)
                    ? 'text-blue-600'
                    : 'text-gray-600 hover:text-blue-600'
                }`}
                onClick={() => setIsMenuOpen(false)}
              >
                {item.name}
              </Link>
            ))}

            {/* Mobile Report Section */}
            <div className="pt-4 border-t border-gray-100">
              <h3 className="text-sm font-semibold text-gray-900 mb-3">Report Emergency</h3>
              <div className="space-y-2">
                {reportTypes.slice(0, 4).map((report, index) => (
                  <Link
                    key={index}
                    to={report.path}
                    onClick={() => setIsMenuOpen(false)}
                    className="flex items-center p-3 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <div className={`p-2 rounded-lg ${report.bgColor} mr-3`}>
                      <report.icon size={16} className={report.color} />
                    </div>
                    <div className="flex-1">
                      <div className="text-sm font-medium text-gray-900">{report.name}</div>
                      <div className="text-xs text-gray-500">{report.description}</div>
                    </div>
                  </Link>
                ))}
              </div>

              <Link
                to="/report/new"
                className="block w-full bg-gradient-to-r from-red-500 to-orange-500 text-white px-6 py-3 rounded-lg hover:from-red-600 hover:to-orange-600 transition-all duration-200 font-semibold text-center mt-4"
                onClick={() => setIsMenuOpen(false)}
              >
                Report Emergency
              </Link>
            </div>
          </div>
        </div>
      )}
    </header>
  );
};

export default Header;
