import React, { useState, useEffect, Suspense } from 'react';
import { Link } from 'react-router-dom';
import {
  AlertTriangle,
  Users,
  CheckCircle,
  Clock,
  ArrowRight,
  ChevronUp,
  Shield,
  MapPin,
  MessageSquare,
  Zap,
  Heart,
  Globe,
  Navigation,
  ZoomIn,
  ZoomOut,
  Crosshair
} from 'lucide-react';
import { format } from 'date-fns';

// Components
import Header from '../components/Layout/Header';
import Footer from '../components/Layout/Footer';
import ChatWidget from '../components/Chat/ChatWidget';
import ViewReportsButton from '../components/Common/ViewReportsButton';
import LoadingSpinner from '../components/Common/LoadingSpinner';

// Data
import { mockReports, mockStatistics } from '../data/mockData';

// Lazy load the map component
const ReportMap = React.lazy(() => import('../components/Map/ReportMap'));

const Home: React.FC = () => {
  const [showScrollTop, setShowScrollTop] = useState(false);
  const [mapLoaded, setMapLoaded] = useState(false);
  const [mapZoom, setMapZoom] = useState(4);
  const [currentLocation, setCurrentLocation] = useState<{ lat: number; lng: number } | null>(null);

  // Statistics data
  const stats = [
    {
      label: "Reports Submitted",
      value: "2,847",
      icon: AlertTriangle,
      color: "text-red-600",
    },
    {
      label: "Lives Helped",
      value: "12,450",
      icon: Users,
      color: "text-blue-600",
    },
    {
      label: "Verified Reports",
      value: "2,189",
      icon: CheckCircle,
      color: "text-green-600",
    },
    {
      label: "Response Time",
      value: "< 2hrs",
      icon: Clock,
      color: "text-purple-600",
    },
  ];

  // Scroll to top functionality
  useEffect(() => {
    const handleScroll = () => {
      setShowScrollTop(window.scrollY > 300);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth',
    });
  };

  // Get current location
  const getCurrentLocation = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const { latitude, longitude } = position.coords;
          setCurrentLocation({ lat: latitude, lng: longitude });
        },
        (error) => {
          console.error('Error getting location:', error);
        }
      );
    }
  };

  // Get recent reports for the map
  const recentReports = mockReports.slice(0, 4);

  // Get color classes for statistics
  const getStatColor = (color: string) => {
    switch (color) {
      case 'text-red-600':
        return 'from-red-500 to-red-600';
      case 'text-blue-600':
        return 'from-blue-500 to-blue-600';
      case 'text-green-600':
        return 'from-green-500 to-green-600';
      case 'text-purple-600':
        return 'from-purple-500 to-purple-600';
      default:
        return 'from-gray-500 to-gray-600';
    }
  };

  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      <main>
        {/* Hero Section */}
        <section className="relative bg-gradient-to-br from-gray-900 via-blue-900 to-indigo-900 text-white overflow-hidden">
          <div className="absolute inset-0 bg-black/10"></div>
          <div className="absolute inset-0">
            <div className="absolute top-20 left-10 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl"></div>
            <div className="absolute bottom-20 right-10 w-80 h-80 bg-purple-500/10 rounded-full blur-3xl"></div>
          </div>

          <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
            <div className="text-center max-w-4xl mx-auto">
              <h1 className="text-5xl md:text-7xl font-bold leading-tight mb-8">
                Unite Communities in
                <br />
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-300 via-purple-300 to-indigo-300">
                  Times of Crisis
                </span>
              </h1>
              <p className="text-xl text-blue-100 mb-12 leading-relaxed max-w-2xl mx-auto">
                Connect with your community during emergencies. Report incidents, offer
                help, and stay informed about local disasters and relief efforts.
              </p>

              <div className="flex flex-col sm:flex-row gap-6 justify-center mb-16">
                <Link
                  to="/report/new"
                  className="group bg-gradient-to-r from-blue-500 to-indigo-600 text-white px-10 py-5 rounded-2xl text-lg font-semibold hover:from-blue-600 hover:to-indigo-700 transition-all transform hover:-translate-y-1 hover:shadow-2xl flex items-center justify-center border border-blue-400/20"
                >
                  Report an Impact
                  <ArrowRight
                    size={20}
                    className="ml-2 group-hover:translate-x-1 transition-transform"
                  />
                </Link>
                
                <ViewReportsButton
                  variant="outline"
                  size="lg"
                  className="border-2 border-white/30 text-white hover:bg-white/10 backdrop-blur-sm"
                />
              </div>

              {/* Quick Stats */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto">
                {stats.map((stat, index) => (
                  <div key={index} className="text-center">
                    <div className="text-3xl md:text-4xl font-bold text-white mb-2">
                      {stat.value}
                    </div>
                    <div className="text-blue-200 text-sm">{stat.label}</div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Statistics Section */}
        <section className="py-16 bg-gray-50" aria-label="Statistics">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
                Making a Real Impact
              </h2>
              <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                Our community-driven platform has helped thousands of people during emergencies
              </p>
            </div>

            <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
              {stats.map((stat, index) => (
                <div
                  key={index}
                  className="bg-white rounded-2xl p-8 shadow-sm border border-gray-100 hover:shadow-lg transition-shadow"
                >
                  <div className="flex items-center space-x-4">
                    <div
                      className={`p-4 rounded-2xl bg-gradient-to-br ${getStatColor(
                        stat.color
                      )} text-white shadow-lg`}
                    >
                      <stat.icon size={32} />
                    </div>
                    <div>
                      <div className="text-3xl font-bold text-gray-900">{stat.value}</div>
                      <div className="text-gray-600">{stat.label}</div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>



        {/* Enhanced Interactive Map Section */}
        <section className="py-24 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                Real-Time Disaster
                <br />
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-red-500 to-orange-500">
                  Monitoring Map
                </span>
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Track active disasters and community response efforts across the nation in real-time
              </p>
            </div>

            <div className="bg-white rounded-3xl shadow-xl border border-gray-100 overflow-hidden">
              <div className="p-8">
                <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8">
                  <div>
                    <h3 className="text-2xl font-semibold text-gray-900 mb-2">Live Disaster Reports</h3>
                    <p className="text-gray-600">Click on any marker to view detailed information</p>
                  </div>
                  <div className="mt-4 lg:mt-0 flex items-center space-x-4">
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
                      <span className="text-sm text-gray-600">Active Reports</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                      <span className="text-sm text-gray-600">Verified</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                      <span className="text-sm text-gray-600">Pending</span>
                    </div>
                  </div>
                </div>

                {/* Enhanced Map Container with Controls */}
                <div className="relative rounded-2xl overflow-hidden border border-gray-200 shadow-lg">
                  {/* Map Loading Placeholder */}
                  {!mapLoaded && (
                    <div className="absolute inset-0 bg-gray-100 flex items-center justify-center z-10">
                      <div className="text-center">
                        <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
                        <p className="text-gray-600 font-medium">Loading interactive map...</p>
                        <div className="w-32 h-2 bg-gray-200 rounded-full mx-auto mt-2 overflow-hidden">
                          <div className="h-full bg-blue-500 rounded-full animate-pulse" style={{ width: '70%' }}></div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Map Controls */}
                  <div className="absolute top-4 right-4 z-20 flex flex-col space-y-2">
                    {/* Current Location Button */}
                    <button
                      onClick={getCurrentLocation}
                      className="group bg-white/90 backdrop-blur-sm p-3 rounded-xl shadow-lg border border-gray-200 hover:bg-white hover:shadow-xl transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      aria-label="Get current location"
                      style={{ minWidth: '44px', minHeight: '44px' }}
                    >
                      <Navigation size={18} className="text-blue-600 group-hover:text-blue-700 transition-colors" />
                    </button>

                    {/* Zoom Controls */}
                    <div className="bg-white/90 backdrop-blur-sm rounded-xl shadow-lg border border-gray-200 overflow-hidden">
                      <button
                        onClick={() => setMapZoom(prev => Math.min(prev + 1, 18))}
                        className="block w-full p-3 hover:bg-gray-50 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500"
                        aria-label="Zoom in"
                        style={{ minWidth: '44px', minHeight: '44px' }}
                      >
                        <ZoomIn size={18} className="text-gray-600 hover:text-gray-800 transition-colors mx-auto" />
                      </button>
                      <div className="border-t border-gray-200"></div>
                      <button
                        onClick={() => setMapZoom(prev => Math.max(prev - 1, 1))}
                        className="block w-full p-3 hover:bg-gray-50 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500"
                        aria-label="Zoom out"
                        style={{ minWidth: '44px', minHeight: '44px' }}
                      >
                        <ZoomOut size={18} className="text-gray-600 hover:text-gray-800 transition-colors mx-auto" />
                      </button>
                    </div>

                    {/* Map Type Toggle */}
                    <button
                      className="bg-white/90 backdrop-blur-sm p-3 rounded-xl shadow-lg border border-gray-200 hover:bg-white hover:shadow-xl transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      aria-label="Toggle map view"
                      style={{ minWidth: '44px', minHeight: '44px' }}
                    >
                      <Globe size={18} className="text-gray-600 hover:text-gray-800 transition-colors" />
                    </button>
                  </div>

                  {/* Map Legend */}
                  <div className="absolute bottom-4 left-4 z-20 bg-white/90 backdrop-blur-sm rounded-xl shadow-lg border border-gray-200 p-4">
                    <h4 className="font-semibold text-gray-900 mb-2 text-sm">Map Legend</h4>
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                        <span className="text-xs text-gray-600">Critical</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
                        <span className="text-xs text-gray-600">High Priority</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                        <span className="text-xs text-gray-600">Medium</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                        <span className="text-xs text-gray-600">Resolved</span>
                      </div>
                    </div>
                  </div>

                  {/* Current Location Indicator */}
                  {currentLocation && (
                    <div className="absolute top-4 left-4 z-20 bg-blue-500 text-white px-3 py-2 rounded-lg shadow-lg text-sm font-medium">
                      <div className="flex items-center space-x-2">
                        <Crosshair size={14} />
                        <span>Your Location</span>
                      </div>
                    </div>
                  )}

                  {/* Main Map */}
                  <div
                    className="transition-opacity duration-500"
                    style={{ opacity: mapLoaded ? 1 : 0 }}
                  >
                    <Suspense fallback={<LoadingSpinner size={32} />}>
                      <ReportMap
                        reports={recentReports}
                        height="500px"
                        onReportSelect={(report) => {
                          console.log('Selected report:', report);
                        }}
                      />
                    </Suspense>
                  </div>
                </div>

                <div className="mt-8 flex flex-col sm:flex-row gap-4 justify-center">
                  <ViewReportsButton
                    size="lg"
                    className="flex-1 sm:flex-none transform hover:scale-105 transition-transform duration-200"
                  />
                  <Link
                    to="/report/new"
                    className="flex-1 sm:flex-none bg-gradient-to-r from-red-500 to-orange-500 text-white px-8 py-4 rounded-xl hover:from-red-600 hover:to-orange-600 transition-all duration-200 font-semibold flex items-center justify-center transform hover:scale-105 shadow-lg hover:shadow-xl"
                  >
                    <AlertTriangle size={20} className="mr-2" />
                    Report New Incident
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Recent Reports Section */}
        <section className="py-24 bg-white" aria-label="Recent Reports">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
                Recent Community Reports
              </h2>
              <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                Stay informed about recent incidents in your area and see how the community
                is responding to emergencies
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
              {mockReports.slice(0, 3).map((report) => (
                <div
                  key={report.id}
                  className="bg-white rounded-2xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-lg transition-shadow group"
                >
                  {report.images && report.images[0] && (
                    <div className="aspect-w-16 aspect-h-9 bg-gray-200">
                      <img
                        src={report.images[0]}
                        alt={report.title}
                        className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                      />
                    </div>
                  )}

                  <div className="p-6">
                    <div className="flex items-center space-x-2 mb-3">
                      <div className={`w-3 h-3 rounded-full ${
                        report.severity === 'critical' ? 'bg-red-500' :
                        report.severity === 'high' ? 'bg-orange-500' :
                        report.severity === 'medium' ? 'bg-yellow-500' : 'bg-green-500'
                      }`}></div>
                      <span className="text-sm text-gray-600 capitalize">{report.type}</span>
                      <span className="text-sm text-gray-400">•</span>
                      <span className="text-sm text-gray-600">
                        {format(new Date(report.createdAt), 'MMM d, yyyy')}
                      </span>
                    </div>

                    <h3 className="text-xl font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
                      {report.title}
                    </h3>
                    <p className="text-gray-600 text-sm mb-4 line-clamp-3">
                      {report.description}
                    </p>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <MapPin size={16} className="text-gray-400" />
                        <span className="text-sm text-gray-600">{report.location}</span>
                      </div>
                      {report.verified && (
                        <div className="flex items-center space-x-1">
                          <CheckCircle size={16} className="text-green-500" />
                          <span className="text-sm text-green-600">Verified</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            <div className="text-center">
              <ViewReportsButton size="lg" />
            </div>
          </div>
        </section>

        {/* Partners Section */}
        <section className="py-24 bg-gray-50" aria-label="Partners">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Trusted by leading organizations worldwide
              </h2>
              <div className="w-24 h-1 bg-gradient-to-r from-blue-500 to-purple-500 mx-auto rounded-full"></div>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-8 lg:gap-12">
              {[
                {
                  name: "American Red Cross",
                  logo: "https://images.pexels.com/photos/6647049/pexels-photo-6647049.jpeg?auto=compress&cs=tinysrgb&w=150&h=80&dpr=1"
                },
                {
                  name: "FEMA",
                  logo: "https://images.pexels.com/photos/8728562/pexels-photo-8728562.jpeg?auto=compress&cs=tinysrgb&w=150&h=80&dpr=1"
                },
                {
                  name: "Salvation Army",
                  logo: "https://images.pexels.com/photos/6995122/pexels-photo-6995122.jpeg?auto=compress&cs=tinysrgb&w=150&h=80&dpr=1"
                },
                {
                  name: "United Way",
                  logo: "https://images.pexels.com/photos/6646917/pexels-photo-6646917.jpeg?auto=compress&cs=tinysrgb&w=150&h=80&dpr=1"
                },
                {
                  name: "Doctors Without Borders",
                  logo: "https://images.pexels.com/photos/4386467/pexels-photo-4386467.jpeg?auto=compress&cs=tinysrgb&w=150&h=80&dpr=1"
                },
                {
                  name: "World Health Organization",
                  logo: "https://images.pexels.com/photos/3786157/pexels-photo-3786157.jpeg?auto=compress&cs=tinysrgb&w=150&h=80&dpr=1"
                },
              ].map((partner, index) => (
                <div
                  key={index}
                  className="flex flex-col items-center text-center group"
                >
                  <div className="bg-white rounded-2xl p-4 shadow-sm border border-gray-200 hover:shadow-lg transition-all duration-300 group-hover:-translate-y-1 mb-4 w-full aspect-[3/2] overflow-hidden">
                    <img
                      src={partner.logo}
                      alt={`${partner.name} logo`}
                      className="w-full h-full object-cover rounded-lg grayscale group-hover:grayscale-0 transition-all duration-300"
                    />
                  </div>
                  <h3 className="text-sm font-medium text-gray-700 group-hover:text-gray-900 transition-colors">
                    {partner.name}
                  </h3>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Call-to-Action Section */}
        <section className="py-24 bg-gradient-to-br from-blue-600 via-blue-700 to-indigo-800 text-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              Ready to Make a Difference?
            </h2>
            <p className="text-xl text-blue-100 mb-12 max-w-2xl mx-auto leading-relaxed">
              Join thousands of community members who are helping to create safer, more
              resilient communities. Every report matters, every action counts.
            </p>

            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              <Link
                to="/report/new"
                className="group bg-white text-blue-600 px-10 py-5 rounded-2xl text-lg font-semibold hover:bg-gray-50 transition-all transform hover:-translate-y-1 hover:shadow-2xl flex items-center justify-center"
              >
                Report an Emergency
                <AlertTriangle
                  size={20}
                  className="ml-2 group-hover:scale-110 transition-transform"
                />
              </Link>

              <Link
                to="/volunteer"
                className="group border-2 border-white/30 text-white px-10 py-5 rounded-2xl text-lg font-semibold hover:bg-white/10 backdrop-blur-sm transition-all transform hover:-translate-y-1 flex items-center justify-center"
              >
                Become a Volunteer
                <Heart
                  size={20}
                  className="ml-2 group-hover:scale-110 transition-transform"
                />
              </Link>
            </div>
          </div>
        </section>
      </main>

      <Footer />
      <ChatWidget />

      {/* Scroll to Top Button */}
      {showScrollTop && (
        <button
          onClick={scrollToTop}
          className="fixed bottom-24 right-6 z-50 bg-blue-600 text-white p-3 rounded-full shadow-lg hover:bg-blue-700 transition-all duration-200 transform hover:scale-110"
          aria-label="Scroll to top"
        >
          <ChevronUp size={20} />
        </button>
      )}
    </div>
  );
};

export default Home;
// Enhanced map section with improved design
